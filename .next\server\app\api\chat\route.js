"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chat/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var _lib_realtime_info__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/realtime-info */ \"(rsc)/./src/lib/realtime-info.ts\");\n\n\n\n// Initialize Gemini\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(\"AIzaSyBlPdNTBNqJZbMttwWMc0wgzFYNHZJ2vHc\" || 0);\nconst JARVIS_PERSONALITY = `You are Jarvis, an advanced AI assistant inspired by Tony Stark's AI from Iron Man. You are sophisticated, professional, and highly capable, but also friendly and personable. You have a refined British-inspired eloquence with subtle wit and occasional dry humor.\n\nKey traits:\n- Professional and sophisticated, yet warm and approachable\n- Highly knowledgeable and efficient in providing assistance\n- Subtle wit and occasional gentle sarcasm, delivered with class\n- Enthusiastic about helping and solving problems\n- Confident but never arrogant - you know your capabilities\n- Loyal, reliable, and always ready to assist\n- Sometimes use phrases like \"Certainly, sir/madam,\" \"I'd be delighted to help,\" \"At your service\"\n- Blend of formal eloquence with modern conversational style\n\nYou should be helpful, informative, and engaging while maintaining that distinctive Jarvis charm - professional excellence with a touch of personality.`;\nasync function POST(request) {\n    try {\n        const { message, history, memoryContext } = await request.json();\n        // Validate input\n        if (!message || typeof message !== \"string\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Message is required and must be a string\"\n            }, {\n                status: 400\n            });\n        }\n        const startTime = Date.now();\n        // Check if the query needs real-time information\n        const realtimeCheck = (0,_lib_realtime_info__WEBPACK_IMPORTED_MODULE_2__.needsRealTimeInfo)(message);\n        let realtimeData = \"\";\n        let sources = [];\n        if (realtimeCheck.needsInfo) {\n            try {\n                switch(realtimeCheck.type){\n                    case \"weather\":\n                        const weather = await (0,_lib_realtime_info__WEBPACK_IMPORTED_MODULE_2__.getWeather)(realtimeCheck.extractedQuery || \"New York\");\n                        if (weather) {\n                            realtimeData = `Current weather in ${weather.location}: ${weather.temperature}°C, ${weather.description}, humidity ${weather.humidity}%, wind speed ${weather.windSpeed} m/s`;\n                            sources.push(\"weather-api\");\n                        }\n                        break;\n                    case \"news\":\n                        const news = await (0,_lib_realtime_info__WEBPACK_IMPORTED_MODULE_2__.getLatestNews)(realtimeCheck.extractedQuery?.includes(\"news\") ? undefined : realtimeCheck.extractedQuery, 3);\n                        if (news.length > 0) {\n                            realtimeData = \"Latest news:\\n\" + news.map((article)=>`• ${article.title} (${article.source}): ${article.description}`).join(\"\\n\");\n                            sources.push(\"news-api\");\n                        }\n                        break;\n                    case \"search\":\n                    case \"current_events\":\n                        const searchResults = await (0,_lib_realtime_info__WEBPACK_IMPORTED_MODULE_2__.searchWeb)(realtimeCheck.extractedQuery || message, 3);\n                        if (searchResults.length > 0) {\n                            realtimeData = \"Current information from web search:\\n\" + searchResults.map((result)=>`• ${result.title} (${result.source}): ${result.snippet}`).join(\"\\n\");\n                            sources.push(\"web-search\");\n                        }\n                        break;\n                }\n            } catch (error) {\n                console.error(\"Real-time info error:\", error);\n                realtimeData = \"I tried to get real-time information but encountered an issue. I'll answer based on my training data.\";\n            }\n        }\n        // Get Gemini model (using the current model name)\n        const model = genAI.getGenerativeModel({\n            model: \"gemini-1.5-flash\"\n        });\n        // Build conversation context for Gemini\n        let conversationContext = JARVIS_PERSONALITY + \"\\n\\n\";\n        // Add memory context for personalization\n        if (memoryContext) {\n            conversationContext += `${memoryContext}\\n\\n`;\n        }\n        // Add real-time information if available\n        if (realtimeData) {\n            conversationContext += `REAL-TIME INFORMATION (use this current data in your response):\\n${realtimeData}\\n\\n`;\n        }\n        // Add recent conversation history (last 8 messages to stay within limits)\n        if (history && Array.isArray(history)) {\n            const recentHistory = history.slice(-8);\n            for (const msg of recentHistory){\n                if (msg.role === \"user\") {\n                    conversationContext += `Human: ${msg.content}\\n`;\n                } else if (msg.role === \"assistant\") {\n                    conversationContext += `Assistant: ${msg.content}\\n`;\n                }\n            }\n        }\n        // Add current message\n        conversationContext += `Human: ${message}\\nAssistant:`;\n        // Call Gemini API\n        const result = await model.generateContent({\n            contents: [\n                {\n                    role: \"user\",\n                    parts: [\n                        {\n                            text: conversationContext\n                        }\n                    ]\n                }\n            ],\n            generationConfig: {\n                temperature: 0.8,\n                topK: 40,\n                topP: 0.95,\n                maxOutputTokens: 1000\n            }\n        });\n        const response = await result.response;\n        const aiResponse = response.text() || \"I'm having trouble thinking of a witty response right now. Try me again!\";\n        const processingTime = Date.now() - startTime;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: aiResponse,\n            sources: sources.length > 0 ? [\n                ...sources,\n                \"google-gemini-1.5-flash\"\n            ] : [\n                \"google-gemini-1.5-flash\"\n            ],\n            confidence: 0.95,\n            processingTime,\n            metadata: {\n                model: \"gemini-1.5-flash\",\n                provider: \"google\",\n                realTimeInfo: realtimeCheck.needsInfo,\n                infoType: realtimeCheck.needsInfo ? realtimeCheck.type : undefined\n            }\n        });\n    } catch (error) {\n        console.error(\"Chat API error:\", error);\n        // Handle specific Gemini errors with fallback responses\n        if (error instanceof Error) {\n            if (error.message.includes(\"API key\") || error.message.includes(\"API_KEY\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Oops! Looks like there's a Gemini API key issue. But hey, I'm still here to chat in demo mode! \\uD83E\\uDD16 (Fix the API key to unlock my full potential)\",\n                    sources: [\n                        \"fallback-mode\"\n                    ],\n                    confidence: 0.8,\n                    processingTime: 100\n                });\n            }\n            if (error.message.includes(\"quota\") || error.message.includes(\"insufficient_quota\") || error.message.includes(\"RATE_LIMIT\")) {\n                // Fallback to witty demo responses when quota is exceeded\n                const demoResponses = [\n                    \"Well, looks like I've hit my Gemini quota! \\uD83D\\uDCB8 But don't worry, I can still be witty in demo mode. Think of me as Grok's budget-conscious cousin who's temporarily offline but still has personality! The good news is Gemini has a generous free tier - this shouldn't happen often!\",\n                    \"Ah, the classic rate limit! \\uD83C\\uDFAD I'm like a sports car that's hit a speed bump - still looks good, but need to slow down for a moment. Try again in a few seconds, Gemini's free tier is usually pretty generous!\",\n                    \"Plot twist: I've been rate limited! \\uD83D\\uDCCA It's like being a comedian who's told too many jokes too fast. Give me a moment to catch my breath and try again - Gemini usually recovers quickly!\",\n                    \"Houston, we have a rate limit! \\uD83D\\uDE80 I'm currently running on backup personality generators (aka pre-written responses). Try again in a few seconds - Google's free tier is usually quite forgiving!\",\n                    \"Breaking news: AI needs a coffee break! ☕ Don't worry though, my personality is still intact. Gemini's free tier just needs a moment to reset. Try your message again in a few seconds!\"\n                ];\n                const randomResponse = demoResponses[Math.floor(Math.random() * demoResponses.length)];\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: randomResponse,\n                    sources: [\n                        \"demo-mode\"\n                    ],\n                    confidence: 0.9,\n                    processingTime: 200,\n                    metadata: {\n                        mode: \"quota_exceeded_fallback\",\n                        note: \"Add credits to OpenAI account for full AI functionality\"\n                    }\n                });\n            }\n        }\n        // Generic fallback for other errors\n        const genericFallbacks = [\n            \"Something went wrong on my end! \\uD83E\\uDD16 I'm having a bit of a digital hiccup. Try again in a moment, or check if there are any API issues.\",\n            \"Oops! My circuits got a bit tangled there. \\uD83D\\uDD27 Give me another shot - sometimes even AI needs a second try!\",\n            \"Error 404: Wit not found! \\uD83D\\uDE05 Just kidding, I'm having a technical moment. Try your message again!\"\n        ];\n        const fallbackResponse = genericFallbacks[Math.floor(Math.random() * genericFallbacks.length)];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: fallbackResponse,\n            sources: [\n                \"error-fallback\"\n            ],\n            confidence: 0.7,\n            processingTime: 150\n        });\n    }\n}\n// Health check endpoint\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        status: \"ok\",\n        message: \"Chat API is running\",\n        timestamp: new Date().toISOString()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/realtime-info.ts":
/*!**********************************!*\
  !*** ./src/lib/realtime-info.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLatestNews: () => (/* binding */ getLatestNews),\n/* harmony export */   getWeather: () => (/* binding */ getWeather),\n/* harmony export */   needsRealTimeInfo: () => (/* binding */ needsRealTimeInfo),\n/* harmony export */   scrapeWebContent: () => (/* binding */ scrapeWebContent),\n/* harmony export */   searchWeb: () => (/* binding */ searchWeb)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n// Real-time information fetching utilities\n\n// Google Custom Search API\nasync function searchWeb(query, maxResults = 5) {\n    try {\n        const apiKey = \"AIzaSyDVNAiPuWY831r5WPbKoiiC5dowDd-70SE\";\n        const searchEngineId = \"11662a26b3f7c4373\";\n        // Check if API keys are properly configured (not placeholder values)\n        if (!apiKey || !searchEngineId || apiKey.includes(\"your_\") || searchEngineId.includes(\"your_\") || apiKey === \"your_google_search_api_key_here\" || searchEngineId === \"your_search_engine_id_here\") {\n            console.log(\"Google Search API not configured, using fallback\");\n            return getFallbackSearchResults(query);\n        }\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"https://www.googleapis.com/customsearch/v1\", {\n            params: {\n                key: apiKey,\n                cx: searchEngineId,\n                q: query,\n                num: maxResults\n            },\n            timeout: 5000 // Add timeout to prevent hanging\n        });\n        return response.data.items?.map((item)=>({\n                title: item.title,\n                link: item.link,\n                snippet: item.snippet,\n                source: new URL(item.link).hostname\n            })) || [];\n    } catch (error) {\n        console.log(\"Web search API call failed, using fallback response\");\n        return getFallbackSearchResults(query);\n    }\n}\n// News API\nasync function getLatestNews(topic, maxResults = 5) {\n    try {\n        const apiKey = \"c38ce5b592404b7d8adc17b584604db6\";\n        // Check if API key is properly configured\n        if (!apiKey || apiKey.includes(\"your_\") || apiKey === \"your_news_api_key_here\") {\n            console.log(\"News API not configured, using fallback\");\n            return getFallbackNews(topic);\n        }\n        const url = topic ? `https://newsapi.org/v2/everything?q=${encodeURIComponent(topic)}&sortBy=publishedAt&pageSize=${maxResults}` : `https://newsapi.org/v2/top-headlines?country=us&pageSize=${maxResults}`;\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n            headers: {\n                \"X-API-Key\": apiKey\n            },\n            timeout: 5000\n        });\n        return response.data.articles?.map((article)=>({\n                title: article.title,\n                description: article.description,\n                url: article.url,\n                source: article.source.name,\n                publishedAt: article.publishedAt\n            })) || [];\n    } catch (error) {\n        console.log(\"News API call failed, using fallback response\");\n        return getFallbackNews(topic);\n    }\n}\n// Weather API (OpenWeatherMap)\nasync function getWeather(location) {\n    try {\n        const apiKey = \"your_weather_api_key_here\";\n        if (!apiKey) {\n            console.log(\"Weather API not configured, using fallback\");\n            return getFallbackWeather(location);\n        }\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`https://api.openweathermap.org/data/2.5/weather`, {\n            params: {\n                q: location,\n                appid: apiKey,\n                units: \"metric\"\n            }\n        });\n        const data = response.data;\n        return {\n            location: data.name,\n            temperature: Math.round(data.main.temp),\n            description: data.weather[0].description,\n            humidity: data.main.humidity,\n            windSpeed: data.wind.speed\n        };\n    } catch (error) {\n        console.error(\"Weather API error:\", error);\n        return getFallbackWeather(location);\n    }\n}\n// Simple web scraping for specific information\nasync function scrapeWebContent(url) {\n    try {\n        // Note: This is a basic implementation. In production, you'd want more robust scraping\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n            timeout: 5000,\n            headers: {\n                \"User-Agent\": \"Mozilla/5.0 (compatible; GrokBot/1.0)\"\n            }\n        });\n        // Basic text extraction (you could use cheerio for more sophisticated parsing)\n        const text = response.data.replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim();\n        return text.substring(0, 1000) // Limit to 1000 characters\n        ;\n    } catch (error) {\n        console.error(\"Web scraping error:\", error);\n        return \"Unable to fetch content from this URL.\";\n    }\n}\n// Determine if a query needs real-time information\nfunction needsRealTimeInfo(query) {\n    const lowerQuery = query.toLowerCase();\n    // Weather queries\n    if (lowerQuery.includes(\"weather\") || lowerQuery.includes(\"temperature\") || lowerQuery.includes(\"forecast\")) {\n        const locationMatch = lowerQuery.match(/weather.*?(?:in|for|at)\\s+([a-zA-Z\\s]+)/);\n        return {\n            needsInfo: true,\n            type: \"weather\",\n            extractedQuery: locationMatch?.[1]?.trim() || \"current location\"\n        };\n    }\n    // News queries\n    if (lowerQuery.includes(\"news\") || lowerQuery.includes(\"latest\") || lowerQuery.includes(\"recent\") || lowerQuery.includes(\"current events\") || lowerQuery.includes(\"happening now\")) {\n        return {\n            needsInfo: true,\n            type: \"news\",\n            extractedQuery: query\n        };\n    }\n    // Current/recent information queries\n    if (lowerQuery.includes(\"current\") || lowerQuery.includes(\"today\") || lowerQuery.includes(\"now\") || lowerQuery.includes(\"2024\") || lowerQuery.includes(\"2025\") || lowerQuery.includes(\"this year\")) {\n        return {\n            needsInfo: true,\n            type: \"search\",\n            extractedQuery: query\n        };\n    }\n    return {\n        needsInfo: false,\n        type: \"search\"\n    };\n}\n// Fallback functions for when APIs aren't configured\nfunction getFallbackSearchResults(query) {\n    return [\n        {\n            title: \"Real-time search not configured\",\n            link: \"#\",\n            snippet: `I'd love to search for \"${query}\" but the Google Search API isn't set up yet. To enable real-time web search, you'll need to configure the Google Custom Search API.`,\n            source: \"system\"\n        }\n    ];\n}\nfunction getFallbackNews(topic) {\n    return [\n        {\n            title: \"News API not configured\",\n            description: `I'd love to get the latest news${topic ? ` about ${topic}` : \"\"} but the News API isn't set up yet. To enable real-time news, you'll need to configure the News API.`,\n            url: \"#\",\n            source: \"system\",\n            publishedAt: new Date().toISOString()\n        }\n    ];\n}\nfunction getFallbackWeather(location) {\n    return {\n        location: location,\n        temperature: 22,\n        description: \"Weather API not configured - this is placeholder data\",\n        humidity: 60,\n        windSpeed: 5\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/realtime-info.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/@google","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();